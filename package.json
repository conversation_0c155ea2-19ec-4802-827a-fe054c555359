{"name": "fastify-prisma-app", "version": "1.0.0", "description": "Fastify API with TypeScript and Prisma", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev --name init", "prisma:studio": "prisma studio", "lint": "eslint . --ext .ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["fastify", "typescript", "prisma", "postgresql"], "author": "", "license": "MIT", "packageManager": "pnpm@10.10.0", "dependencies": {"@fastify/cors": "^11.0.1", "@fastify/env": "^5.0.2", "@fastify/helmet": "^13.0.1", "@fastify/swagger": "^9.5.1", "@fastify/swagger-ui": "^5.2.3", "@prisma/client": "^6.10.1", "@types/validator": "^13.15.2", "better-auth": "^1.2.12", "dotenv": "^16.4.1", "fastify": "^5.4.0", "fastify-plugin": "^5.0.1", "node-fetch": "^2.7.0", "validator": "^13.15.15"}, "devDependencies": {"@types/node": "^20.11.0", "@types/pino-http": "^5.0.4", "prisma": "6.10.1", "tsx": "^4.7.1", "typescript": "^5.3.3"}}