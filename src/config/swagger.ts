import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import fastifySwagger from '@fastify/swagger';
import fastifySwaggerUI from '@fastify/swagger-ui';
import { auth } from '../lib/auth';

declare module 'fastify' {
  interface FastifySchema {
    hide?: boolean;
  }
}

export async function registerSwagger(app: FastifyInstance): Promise<void> {
  try {
    const authOpenAPISchema = await auth.api.generateOpenAPISchema();
    
    // Register Swagger
    await app.register(fastifySwagger, {
      openapi: {
        ...authOpenAPISchema,
        info: {
          title: 'Life Navigation API',
          description: 'API documentation for Life Navigation application',
          version: '1.0.0',
        },
        servers: [
          { url: 'http://localhost:3002', description: 'Development' },
        ],
        components: {
          securitySchemes: {
            bearerAuth: {
              type: 'http',
              scheme: 'bearer',
              bearerFormat: 'JWT',
            },
          },
        },
      },
      hideUntagged: false // Show all routes, even those without tags
    });

    // Register Swagger UI
    await app.register(fastifySwaggerUI, {
      routePrefix: '/documentation',
      uiConfig: {
        docExpansion: 'list',
        deepLinking: false,
      },
      staticCSP: true,
      transformSpecification: (swaggerObject: any) => {
        // Ensure the base path is correct
        swaggerObject.servers = [{ url: '/' }];
        return swaggerObject;
      },
    });

    // Add a redirect from /documentation to /documentation/
    app.get('/documentation', async (request: FastifyRequest, reply: FastifyReply) => {
      return reply.redirect('/documentation/');
    });

    // Add a redirect from /docs to /documentation
    app.get('/docs', async (request: FastifyRequest, reply: FastifyReply) => {
      return reply.redirect('/documentation/');
    });

    app.log.info('Swagger documentation is available at /documentation');
  } catch (error) {
    app.log.error('Failed to initialize Swagger:', error);
    throw error;
  }
}
